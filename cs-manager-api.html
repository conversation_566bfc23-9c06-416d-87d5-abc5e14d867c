<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المراكز الصحية - النسخة الجديدة</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    
    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            min-height: 100vh;
            direction: rtl;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--primary-gradient);
            color: white;
            padding: 30px;
            border-radius: var(--radius-2xl);
            margin-bottom: 30px;
            text-align: center;
            box-shadow: var(--shadow-xl);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .dashboard-card {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: 30px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .card-icon.primary { background: var(--primary-gradient); }
        .card-icon.success { background: var(--success-gradient); }
        .card-icon.warning { background: var(--warning-gradient); }
        .card-icon.danger { background: var(--danger-gradient); }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-content {
            margin-bottom: 20px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 24px;
            font-size: 1rem;
            font-weight: 600;
            border: none;
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            min-height: 44px;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .analytics-section {
            background: var(--bg-primary);
            border-radius: var(--radius-2xl);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }

        .analytics-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
        }

        .analytics-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .chart-container {
            background: var(--bg-secondary);
            border-radius: var(--radius-xl);
            padding: 25px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chart-canvas-wrapper {
            position: relative;
            height: 300px;
            margin-bottom: 15px;
        }

        .chart-canvas-wrapper canvas {
            max-height: 100%;
            width: 100% !important;
            height: 100% !important;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: var(--text-secondary);
        }

        .loading i {
            margin-left: 10px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .navigation {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .nav-links {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .nav-link {
            background: var(--bg-secondary);
            color: var(--text-primary);
            padding: 10px 20px;
            border-radius: var(--radius-lg);
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .nav-link:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
        }

        .status-indicator {
            padding: 15px;
            border-radius: var(--radius-lg);
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .status-indicator.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #86efac;
        }

        .status-indicator.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fbbf24;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .dashboard-grid,
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .analytics-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-hospital"></i> نظام إدارة المراكز الصحية</h1>
            <p>النسخة الجديدة مع واجهة برمجة التطبيقات المتقدمة</p>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <div class="nav-links">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
                <a href="cs-manager.html" class="nav-link">
                    <i class="fas fa-desktop"></i> النسخة الكاملة
                </a>
                <a href="test-analytics-dashboard.html" class="nav-link">
                    <i class="fas fa-vial"></i> اختبار التحليلات
                </a>
                <a href="#" class="nav-link" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> تحديث البيانات
                </a>
            </div>
        </div>

        <!-- Status Indicator -->
        <div class="status-indicator info" id="statusIndicator">
            <i class="fas fa-info-circle"></i>
            <span>جاري تحميل البيانات من واجهة برمجة التطبيقات...</span>
        </div>

        <!-- Dashboard Cards -->
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon primary">
                        <i class="fas fa-baby"></i>
                    </div>
                    <div class="card-title">الأطفال المسجلين</div>
                </div>
                <div class="card-content">
                    <div class="stat-number" id="totalChildren">0</div>
                    <div class="stat-label">إجمالي الأطفال</div>
                </div>
                <button class="btn btn-primary" onclick="loadChildrenData()">
                    <i class="fas fa-list"></i> عرض التفاصيل
                </button>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon success">
                        <i class="fas fa-syringe"></i>
                    </div>
                    <div class="card-title">التلقيحات المكتملة</div>
                </div>
                <div class="card-content">
                    <div class="stat-number" id="completedVaccinations">0</div>
                    <div class="stat-label">تلقيح مكتمل</div>
                </div>
                <button class="btn btn-primary" onclick="loadVaccinationData()">
                    <i class="fas fa-chart-line"></i> عرض الإحصائيات
                </button>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon warning">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div class="card-title">التسجيلات الشهرية</div>
                </div>
                <div class="card-content">
                    <div class="stat-number" id="monthlyRegistrations">0</div>
                    <div class="stat-label">هذا الشهر</div>
                </div>
                <button class="btn btn-primary" onclick="loadMonthlyData()">
                    <i class="fas fa-calendar-alt"></i> عرض التفاصيل
                </button>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon danger">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="card-title">مستوى المخزون</div>
                </div>
                <div class="card-content">
                    <div class="stat-number" id="stockLevel">0</div>
                    <div class="stat-label">صنف متوفر</div>
                </div>
                <button class="btn btn-primary" onclick="loadStockData()">
                    <i class="fas fa-warehouse"></i> إدارة المخزون
                </button>
            </div>
        </div>

        <!-- Analytics Section -->
        <div class="analytics-section">
            <div class="analytics-header">
                <h2 class="analytics-title">
                    <i class="fas fa-chart-line"></i>
                    تحليل البيانات والإحصائيات
                </h2>
                <button class="btn btn-primary" onclick="initializeCharts()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث الرسوم البيانية
                </button>
            </div>

            <div class="charts-grid">
                <!-- Sample Chart Containers -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-syringe"></i>
                            تغطية التلقيحات
                        </h3>
                    </div>
                    <div class="chart-canvas-wrapper">
                        <canvas id="vaccinationChart"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-baby"></i>
                            اتجاهات التسجيل
                        </h3>
                    </div>
                    <div class="chart-canvas-wrapper">
                        <canvas id="registrationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Client -->
    <script src="js/api-client.js"></script>
    
    <script>
        // Initialize API client
        const apiClient = new ApiClient('/api');
        let chartInstances = {};

        // Update status indicator
        function updateStatus(message, type = 'info') {
            const indicator = document.getElementById('statusIndicator');
            const icons = {
                info: 'fa-info-circle',
                success: 'fa-check-circle',
                error: 'fa-exclamation-circle',
                warning: 'fa-exclamation-triangle'
            };
            
            indicator.className = `status-indicator ${type}`;
            indicator.innerHTML = `<i class="fas ${icons[type]}"></i><span>${message}</span>`;
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                updateStatus('جاري تحميل البيانات...', 'info');
                
                const stats = await apiClient.getStats();
                
                document.getElementById('totalChildren').textContent = stats.total_children || 0;
                document.getElementById('completedVaccinations').textContent = stats.completed_vaccinations || 0;
                document.getElementById('monthlyRegistrations').textContent = stats.monthly_registrations || 0;
                document.getElementById('stockLevel').textContent = '0'; // Placeholder
                
                updateStatus('تم تحميل البيانات بنجاح', 'success');
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                updateStatus('خطأ في تحميل البيانات', 'error');
            }
        }

        // Initialize charts
        function initializeCharts() {
            updateStatus('جاري تهيئة الرسوم البيانية...', 'info');
            
            // Sample vaccination chart
            const vaccinationCtx = document.getElementById('vaccinationChart');
            if (vaccinationCtx && !chartInstances.vaccination) {
                chartInstances.vaccination = new Chart(vaccinationCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['مكتمل', 'قيد الانتظار', 'متأخر'],
                        datasets: [{
                            data: [65, 25, 10],
                            backgroundColor: ['#10b981', '#f59e0b', '#ef4444'],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // Sample registration chart
            const registrationCtx = document.getElementById('registrationChart');
            if (registrationCtx && !chartInstances.registration) {
                chartInstances.registration = new Chart(registrationCtx, {
                    type: 'line',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'التسجيلات الشهرية',
                            data: [12, 19, 15, 22, 18, 25],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            updateStatus('تم تهيئة الرسوم البيانية بنجاح', 'success');
        }

        // Refresh all data
        function refreshData() {
            loadDashboardData();
            initializeCharts();
        }

        // Individual data loading functions
        function loadChildrenData() {
            updateStatus('جاري تحميل بيانات الأطفال...', 'info');
            // Redirect to full system
            window.open('cs-manager.html', '_blank');
        }

        function loadVaccinationData() {
            updateStatus('جاري تحميل بيانات التلقيحات...', 'info');
            window.open('cs-manager.html', '_blank');
        }

        function loadMonthlyData() {
            updateStatus('جاري تحميل البيانات الشهرية...', 'info');
            window.open('cs-manager.html', '_blank');
        }

        function loadStockData() {
            updateStatus('جاري تحميل بيانات المخزون...', 'info');
            window.open('cs-manager.html', '_blank');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('مرحباً بك في النسخة الجديدة من النظام', 'success');
            
            setTimeout(() => {
                loadDashboardData();
                initializeCharts();
            }, 1000);
        });
    </script>
</body>
</html>
