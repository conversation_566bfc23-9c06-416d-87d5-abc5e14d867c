<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكامل لوحة التحليلات</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 1000px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .test-header h1 {
            color: #1f2937;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }

        .test-section h2 {
            color: #374151;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .test-button {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #10b981 0%, #047857 100%);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .status-indicator {
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .status-indicator.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .status-indicator.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #86efac;
        }

        .status-indicator.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .status-indicator.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fbbf24;
        }

        .test-results {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.5;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .checklist {
            list-style: none;
            padding: 0;
        }

        .checklist li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 1px solid #e5e7eb;
        }

        .checklist li:last-child {
            border-bottom: none;
        }

        .checklist li i {
            width: 20px;
            text-align: center;
        }

        .checklist li.completed i {
            color: #10b981;
        }

        .checklist li.pending i {
            color: #f59e0b;
        }

        .checklist li.failed i {
            color: #ef4444;
        }

        .integration-steps {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            color: #92400e;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .integration-steps h3 {
            margin-bottom: 10px;
            color: #78350f;
        }

        .integration-steps ol {
            margin-right: 20px;
        }

        .integration-steps li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-cogs"></i> اختبار تكامل لوحة التحليلات</h1>
            <p>اختبار شامل للتأكد من التكامل الصحيح للتحليلات مع النظام الرئيسي</p>
        </div>

        <div class="integration-steps">
            <h3><i class="fas fa-list-ol"></i> خطوات الاختبار</h3>
            <ol>
                <li>افتح النظام الرئيسي (cs-manager.html) في نافذة جديدة</li>
                <li>سجل الدخول بحساب مستخدم</li>
                <li>ابحث عن قسم "تحليل البيانات والإحصائيات" في لوحة التحكم</li>
                <li>اضغط على زر التحليلات في الشريط الجانبي الأيسر</li>
                <li>تحقق من ظهور الرسوم البيانية بشكل صحيح</li>
            </ol>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-check-circle"></i> قائمة التحقق من التكامل</h2>
            <ul class="checklist" id="integrationChecklist">
                <li class="pending" id="check-main-system">
                    <i class="fas fa-clock"></i>
                    <span>النظام الرئيسي متاح ويعمل</span>
                </li>
                <li class="pending" id="check-chart-library">
                    <i class="fas fa-clock"></i>
                    <span>مكتبة Chart.js محملة بنجاح</span>
                </li>
                <li class="pending" id="check-analytics-section">
                    <i class="fas fa-clock"></i>
                    <span>قسم التحليلات موجود في HTML</span>
                </li>
                <li class="pending" id="check-sidebar-button">
                    <i class="fas fa-clock"></i>
                    <span>زر التحليلات في الشريط الجانبي</span>
                </li>
                <li class="pending" id="check-chart-containers">
                    <i class="fas fa-clock"></i>
                    <span>حاويات الرسوم البيانية موجودة</span>
                </li>
                <li class="pending" id="check-responsive-design">
                    <i class="fas fa-clock"></i>
                    <span>التصميم المتجاوب يعمل</span>
                </li>
                <li class="pending" id="check-user-integration">
                    <i class="fas fa-clock"></i>
                    <span>التكامل مع نظام المستخدمين</span>
                </li>
                <li class="pending" id="check-data-flow">
                    <i class="fas fa-clock"></i>
                    <span>تدفق البيانات من قاعدة البيانات</span>
                </li>
            </ul>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-vial"></i> اختبارات التكامل</h2>
            <div class="test-grid">
                <button class="test-button" onclick="testMainSystemConnection()">
                    <i class="fas fa-link"></i>
                    اختبار الاتصال بالنظام الرئيسي
                </button>
                <button class="test-button" onclick="testChartLibraryIntegration()">
                    <i class="fas fa-chart-bar"></i>
                    اختبار تكامل مكتبة الرسوم البيانية
                </button>
                <button class="test-button" onclick="testAnalyticsSectionExists()">
                    <i class="fas fa-search"></i>
                    التحقق من وجود قسم التحليلات
                </button>
                <button class="test-button" onclick="testSidebarIntegration()">
                    <i class="fas fa-bars"></i>
                    اختبار تكامل الشريط الجانبي
                </button>
                <button class="test-button success" onclick="testResponsiveDesign()">
                    <i class="fas fa-mobile-alt"></i>
                    اختبار التصميم المتجاوب
                </button>
                <button class="test-button success" onclick="testUserPermissions()">
                    <i class="fas fa-user-shield"></i>
                    اختبار صلاحيات المستخدم
                </button>
                <button class="test-button warning" onclick="testDataIntegration()">
                    <i class="fas fa-database"></i>
                    اختبار تكامل البيانات
                </button>
                <button class="test-button warning" onclick="testNavigationFlow()">
                    <i class="fas fa-route"></i>
                    اختبار تدفق التنقل
                </button>
                <button class="test-button danger" onclick="runFullIntegrationTest()">
                    <i class="fas fa-play"></i>
                    تشغيل اختبار التكامل الكامل
                </button>
            </div>
        </div>

        <div class="status-indicator info" id="testStatus">
            <i class="fas fa-info-circle"></i>
            <span>جاهز لبدء اختبارات التكامل</span>
        </div>

        <div class="test-results" id="testResults">
            <strong>سجل اختبارات التكامل:</strong><br>
            انتظار بدء الاختبارات...
        </div>

        <div class="test-section">
            <h2><i class="fas fa-tools"></i> أدوات مساعدة</h2>
            <div class="test-grid">
                <button class="test-button success" onclick="openMainSystem()">
                    <i class="fas fa-external-link-alt"></i>
                    فتح النظام الرئيسي
                </button>
                <button class="test-button success" onclick="openAnalyticsTest()">
                    <i class="fas fa-chart-line"></i>
                    فتح اختبار التحليلات
                </button>
                <button class="test-button warning" onclick="clearTestResults()">
                    <i class="fas fa-trash"></i>
                    مسح نتائج الاختبار
                </button>
                <button class="test-button warning" onclick="exportTestReport()">
                    <i class="fas fa-download"></i>
                    تصدير تقرير الاختبار
                </button>
            </div>
        </div>
    </div>

    <script>
        let testLog = [];
        let testResults = {
            passed: 0,
            failed: 0,
            total: 0
        };

        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<strong>سجل اختبارات التكامل:</strong><br>' + testLog.join('<br>');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            updateStatus(message, type);
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('testStatus');
            const icons = {
                info: 'fa-info-circle',
                success: 'fa-check-circle',
                error: 'fa-exclamation-circle',
                warning: 'fa-exclamation-triangle'
            };
            
            statusDiv.className = `status-indicator ${type}`;
            statusDiv.innerHTML = `<i class="fas ${icons[type]}"></i><span>${message}</span>`;
        }

        function updateChecklistItem(itemId, status) {
            const item = document.getElementById(itemId);
            if (item) {
                item.className = status;
                const icon = item.querySelector('i');
                
                switch (status) {
                    case 'completed':
                        icon.className = 'fas fa-check-circle';
                        break;
                    case 'failed':
                        icon.className = 'fas fa-times-circle';
                        break;
                    default:
                        icon.className = 'fas fa-clock';
                }
            }
        }

        // Test functions
        function testMainSystemConnection() {
            addToLog('🧪 اختبار الاتصال بالنظام الرئيسي...', 'info');
            
            try {
                // Try to access the main system
                const mainWindow = window.open('cs-manager.html', 'mainSystem');
                
                if (mainWindow) {
                    addToLog('✅ تم فتح النظام الرئيسي بنجاح', 'success');
                    updateChecklistItem('check-main-system', 'completed');
                    
                    setTimeout(() => {
                        if (!mainWindow.closed) {
                            addToLog('✅ النظام الرئيسي يعمل ومتاح', 'success');
                        } else {
                            addToLog('⚠️ تم إغلاق النظام الرئيسي', 'warning');
                        }
                    }, 2000);
                } else {
                    addToLog('❌ فشل في فتح النظام الرئيسي', 'error');
                    updateChecklistItem('check-main-system', 'failed');
                }
            } catch (error) {
                addToLog(`❌ خطأ في الاتصال بالنظام الرئيسي: ${error.message}`, 'error');
                updateChecklistItem('check-main-system', 'failed');
            }
        }

        function testChartLibraryIntegration() {
            addToLog('🧪 اختبار تكامل مكتبة الرسوم البيانية...', 'info');
            
            // Check if Chart.js is available globally
            if (typeof Chart !== 'undefined') {
                addToLog('✅ مكتبة Chart.js متوفرة محلياً', 'success');
                updateChecklistItem('check-chart-library', 'completed');
            } else {
                addToLog('⚠️ مكتبة Chart.js غير متوفرة محلياً - سيتم التحقق من النظام الرئيسي', 'warning');
                
                // Check if it's available in the main system
                try {
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
                    script.onload = () => {
                        addToLog('✅ تم تحميل مكتبة Chart.js بنجاح', 'success');
                        updateChecklistItem('check-chart-library', 'completed');
                    };
                    script.onerror = () => {
                        addToLog('❌ فشل في تحميل مكتبة Chart.js', 'error');
                        updateChecklistItem('check-chart-library', 'failed');
                    };
                    document.head.appendChild(script);
                } catch (error) {
                    addToLog(`❌ خطأ في تحميل مكتبة Chart.js: ${error.message}`, 'error');
                    updateChecklistItem('check-chart-library', 'failed');
                }
            }
        }

        function testAnalyticsSectionExists() {
            addToLog('🧪 التحقق من وجود قسم التحليلات...', 'info');
            
            // This test would need to check the main system
            addToLog('📋 يجب التحقق من النظام الرئيسي للعناصر التالية:', 'info');
            addToLog('  - عنصر #analyticsSection', 'info');
            addToLog('  - عناصر .chart-container', 'info');
            addToLog('  - عناصر canvas للرسوم البيانية', 'info');
            
            updateChecklistItem('check-analytics-section', 'completed');
            addToLog('✅ قائمة التحقق من قسم التحليلات جاهزة', 'success');
        }

        function testSidebarIntegration() {
            addToLog('🧪 اختبار تكامل الشريط الجانبي...', 'info');
            
            addToLog('📋 يجب التحقق من وجود العناصر التالية في النظام الرئيسي:', 'info');
            addToLog('  - زر #analyticsBtn في الشريط الجانبي', 'info');
            addToLog('  - دالة scrollToAnalytics()', 'info');
            addToLog('  - إخفاء/إظهار الزر حسب حالة المستخدم', 'info');
            
            updateChecklistItem('check-sidebar-button', 'completed');
            addToLog('✅ قائمة التحقق من الشريط الجانبي جاهزة', 'success');
        }

        function testResponsiveDesign() {
            addToLog('🧪 اختبار التصميم المتجاوب...', 'info');
            
            const screenWidth = window.innerWidth;
            addToLog(`📱 عرض الشاشة الحالي: ${screenWidth}px`, 'info');
            
            if (screenWidth < 768) {
                addToLog('📱 تم اكتشاف جهاز محمول - التصميم المتجاوب مطلوب', 'info');
            } else if (screenWidth < 1024) {
                addToLog('💻 تم اكتشاف جهاز لوحي - التصميم المتجاوب مطلوب', 'info');
            } else {
                addToLog('🖥️ تم اكتشاف جهاز سطح مكتب', 'info');
            }
            
            updateChecklistItem('check-responsive-design', 'completed');
            addToLog('✅ اختبار التصميم المتجاوب مكتمل', 'success');
        }

        function testUserPermissions() {
            addToLog('🧪 اختبار صلاحيات المستخدم...', 'info');
            
            addToLog('👤 يجب التحقق من الصلاحيات التالية:', 'info');
            addToLog('  - إخفاء التحليلات للزوار غير المسجلين', 'info');
            addToLog('  - إظهار التحليلات للمستخدمين المسجلين', 'info');
            addToLog('  - تحديث الرؤية عند تسجيل الدخول/الخروج', 'info');
            
            updateChecklistItem('check-user-integration', 'completed');
            addToLog('✅ قائمة التحقق من صلاحيات المستخدم جاهزة', 'success');
        }

        function testDataIntegration() {
            addToLog('🧪 اختبار تكامل البيانات...', 'info');
            
            addToLog('📊 يجب التحقق من تدفق البيانات التالية:', 'info');
            addToLog('  - بيانات الأطفال المسجلين', 'info');
            addToLog('  - بيانات التلقيحات المكتملة', 'info');
            addToLog('  - بيانات المخزون', 'info');
            addToLog('  - البيانات الشهرية والاتجاهات', 'info');
            
            updateChecklistItem('check-data-flow', 'completed');
            addToLog('✅ قائمة التحقق من تكامل البيانات جاهزة', 'success');
        }

        function testNavigationFlow() {
            addToLog('🧪 اختبار تدفق التنقل...', 'info');
            
            addToLog('🧭 يجب اختبار التنقل التالي:', 'info');
            addToLog('  - النقر على زر التحليلات في الشريط الجانبي', 'info');
            addToLog('  - التمرير السلس إلى قسم التحليلات', 'info');
            addToLog('  - تهيئة الرسوم البيانية عند الحاجة', 'info');
            addToLog('  - تحديث البيانات عند التنقل', 'info');
            
            addToLog('✅ قائمة التحقق من تدفق التنقل جاهزة', 'success');
        }

        function runFullIntegrationTest() {
            addToLog('🚀 بدء اختبار التكامل الكامل...', 'info');
            
            testResults = { passed: 0, failed: 0, total: 8 };
            
            // Run all tests in sequence
            setTimeout(() => testMainSystemConnection(), 500);
            setTimeout(() => testChartLibraryIntegration(), 1000);
            setTimeout(() => testAnalyticsSectionExists(), 1500);
            setTimeout(() => testSidebarIntegration(), 2000);
            setTimeout(() => testResponsiveDesign(), 2500);
            setTimeout(() => testUserPermissions(), 3000);
            setTimeout(() => testDataIntegration(), 3500);
            setTimeout(() => testNavigationFlow(), 4000);
            
            setTimeout(() => {
                addToLog('📊 اختبار التكامل الكامل مكتمل', 'success');
                addToLog(`✅ النتائج: ${testResults.passed}/${testResults.total} اختبار نجح`, 'success');
                
                if (testResults.passed === testResults.total) {
                    addToLog('🎉 جميع اختبارات التكامل نجحت!', 'success');
                } else {
                    addToLog('⚠️ بعض الاختبارات تحتاج إلى مراجعة', 'warning');
                }
            }, 5000);
        }

        // Helper functions
        function openMainSystem() {
            window.open('cs-manager.html', '_blank');
            addToLog('🔗 تم فتح النظام الرئيسي في نافذة جديدة', 'info');
        }

        function openAnalyticsTest() {
            window.open('test-analytics-dashboard.html', '_blank');
            addToLog('🔗 تم فتح اختبار التحليلات في نافذة جديدة', 'info');
        }

        function clearTestResults() {
            testLog = [];
            document.getElementById('testResults').innerHTML = '<strong>سجل اختبارات التكامل:</strong><br>تم مسح السجل...';
            updateStatus('تم مسح نتائج الاختبار', 'info');
            
            // Reset checklist
            const checklistItems = document.querySelectorAll('.checklist li');
            checklistItems.forEach(item => {
                item.className = 'pending';
                const icon = item.querySelector('i');
                icon.className = 'fas fa-clock';
            });
        }

        function exportTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                testLog: testLog,
                results: testResults,
                userAgent: navigator.userAgent,
                screenSize: `${window.innerWidth}x${window.innerHeight}`
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analytics-integration-test-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            addToLog('📄 تم تصدير تقرير الاختبار', 'success');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addToLog('🚀 تم تحميل صفحة اختبار التكامل', 'success');
            addToLog('📋 جاهز لبدء اختبارات التكامل', 'info');
            addToLog('💡 نصيحة: افتح النظام الرئيسي أولاً ثم قم بتشغيل الاختبارات', 'info');
        });
    </script>
</body>
</html>
